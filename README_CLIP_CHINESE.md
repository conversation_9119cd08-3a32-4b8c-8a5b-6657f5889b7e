# Chinese Travelogue Narrative Analysis with CLIP

This document provides comprehensive instructions for using the enhanced pipeline to analyze Chinese travelogues with CLIP embeddings.

## Overview

The enhanced pipeline supports:
- **Chinese text processing** with proper sentence segmentation
- **CLIP embeddings** using OpenAI's pre-trained models
- **Narrative feature extraction** (speed, volume, circuitousness)
- **Exact chunk count** (T=100) for mathematical consistency

## Quick Start

### 1. Install Dependencies

```bash
# Activate the narrative environment
conda activate narrative

# Install PyTorch and CLIP
pip install torch clip-by-openai

# Verify installation
python -c "import torch, clip; print('Dependencies installed successfully')"
```

### 2. Run Chinese Travelogue Analysis

```bash
python main.py --embedding_type clip --language zh --data_file saved/data/data.json --T 100
```

### 3. Expected Output Format

```json
{
  "travelId": "7687900",
  "speed": 1.408708,
  "volume": 0.945340,
  "circuitousness": 0.043203
}
```

## Command-Line Arguments

### Required Arguments
- `--embedding_type clip`: Use CLIP embeddings instead of FastText
- `--language zh`: Enable Chinese text processing
- `--data_file saved/data/data.json`: Path to Chinese travelogue data
- `--T 100`: Generate exactly 100 chunks per document

### Optional Arguments
- `--clip_model ViT-B/32`: CLIP model variant (default: ViT-B/32)
  - `ViT-B/32`: 512-dimensional embeddings (recommended)
  - `ViT-B/16`: 512-dimensional embeddings (higher quality)
  - `ViT-L/14`: 768-dimensional embeddings (best quality)

### Example Commands

```bash
# Basic Chinese travelogue analysis
python main.py --embedding_type clip --language zh --data_file saved/data/data.json --T 100

# Using higher quality CLIP model
python main.py --embedding_type clip --language zh --data_file saved/data/data.json --T 100 --clip_model ViT-B/16

# Using best quality CLIP model
python main.py --embedding_type clip --language zh --data_file saved/data/data.json --T 100 --clip_model ViT-L/14

# Process limited number of documents
python main.py --embedding_type clip --language zh --data_file saved/data/data.json --T 100 --limit 100
```

## Input Data Format

### Chinese Travelogue JSON Structure
```json
[
  {
    "travelId": "7687900",
    "source": "草原观赏季，专程从北京赶到内蒙古乌兰布统大草原北部的汗苏鲁国际生态牧场..."
  },
  {
    "travelId": "7641926", 
    "source": "西藏拉萨的布达拉宫是世界文化遗产，也是西藏最重要的宫殿建筑群..."
  }
]
```

### Required Fields
- `travelId`: Unique identifier for each travelogue
- `source`: Chinese text content of the travelogue

## Output Format

### Individual Travelogue Result
```json
{
  "travelId": "7687900",
  "speed": 1.408708,
  "volume": 0.945340,
  "circuitousness": 0.043203
}
```

### Field Descriptions
- `travelId`: Original travelogue identifier
- `speed`: Narrative speed (average distance between consecutive chunks)
- `volume`: Narrative volume (convex hull volume in embedding space)
- `circuitousness`: Narrative circuitousness (TSP-based path efficiency)

## Technical Details

### Chinese Text Processing
- **Sentence Segmentation**: Uses pattern `[。！？；.!?;]|\n\s*`
- **Chunk Generation**: Exactly 100 chunks per document
- **Short Documents**: Content repetition to reach 100 chunks
- **Long Documents**: Intelligent sentence merging to exactly 100 chunks

### CLIP Embeddings
- **Model**: OpenAI CLIP pre-trained models
- **Dimensions**: 512D (ViT-B/32, ViT-B/16) or 768D (ViT-L/14)
- **Normalization**: L2 normalization applied automatically
- **Device**: Automatic GPU/CPU detection

### Feature Extraction
- **Speed**: Average Euclidean distance between consecutive embeddings
- **Volume**: Minimum volume ellipsoid containing all embeddings
- **Circuitousness**: Ratio of TSP path length to direct path length

## Troubleshooting

### Common Issues

1. **CLIP Dependencies Missing**
   ```bash
   pip install torch clip-by-openai
   ```

2. **CUDA Out of Memory**
   ```bash
   # Use CPU-only mode
   export CUDA_VISIBLE_DEVICES=""
   python main.py --embedding_type clip --language zh --data_file saved/data/data.json --T 100
   ```

3. **Chinese Text Encoding Issues**
   - Ensure data file is saved with UTF-8 encoding
   - Check that JSON is properly formatted

4. **Empty Results**
   - Verify data file path is correct
   - Check that travelogue data has 'source' field
   - Ensure --language zh is specified

### Performance Optimization

1. **Batch Processing**
   - Process documents in batches for large datasets
   - Use --limit parameter to test with smaller subsets

2. **Model Selection**
   - Use ViT-B/32 for fastest processing
   - Use ViT-L/14 for highest quality (slower)

3. **Memory Management**
   - Monitor GPU memory usage with large datasets
   - Consider CPU processing for very large files

## Comparison with FastText

| Aspect | FastText | CLIP |
|--------|----------|------|
| Dimensions | 300 | 512/768 |
| Language Support | Multilingual | Multilingual |
| Training Required | Yes | No (pre-trained) |
| Quality | Good | Excellent |
| Speed | Fast | Moderate |
| Chinese Support | Limited | Excellent |

## Next Steps

1. **Validate Results**: Compare CLIP vs FastText results on sample data
2. **Scale Analysis**: Process full travelogue dataset
3. **Feature Analysis**: Analyze narrative patterns in Chinese travelogues
4. **Visualization**: Create plots of narrative features across travelogues

## Support

For issues or questions:
1. Check this documentation
2. Verify environment setup with test commands
3. Review error messages for specific guidance
4. Ensure all dependencies are properly installed
