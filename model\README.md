# Chinese Travelogue Narrative Analysis

This directory contains the implementation for analyzing Chinese travelogues using CLIP embeddings and extracting narrative features.

## Files

- `chinese_travelogue_analysis.py` - Main implementation with all classes and pipeline
- `test_chinese_analysis.py` - Test suite to verify the implementation
- `README.md` - This documentation file

## Dependencies

Install the required packages:

```bash
conda activate narrative
pip install transformers torch
```

## Quick Start

### 1. Test the Implementation

```bash
cd model
python test_chinese_analysis.py
```

### 2. Run Analysis on Real Data

```bash
cd model
python chinese_travelogue_analysis.py --data_file ../saved/data/data.json
```

### 3. Custom Analysis

```bash
cd model
python chinese_travelogue_analysis.py \
    --data_file ../saved/data/data.json \
    --output_file my_results.json \
    --target_chunks 100 \
    --limit 50
```

## Implementation Details

### Classes

1. **ChineseTextProcessor**
   - Handles Chinese sentence segmentation
   - Generates exactly T=100 chunks per document
   - Uses pattern: `[。！？；.!?;]|\n\s*`

2. **CLIPEmbedding**
   - Uses Hugging Face `openai/clip-vit-base-patch32`
   - Generates 512-dimensional embeddings
   - Applies L2 normalization automatically

3. **NarrativeFeatureExtractor**
   - Extracts speed, volume, and circuitousness
   - Works with any embedding dimension
   - Handles edge cases gracefully

4. **ChineseTravelogueAnalyzer**
   - Main pipeline orchestrator
   - Batch processing support
   - Summary statistics generation

### Input Format

JSON file with Chinese travelogue data:

```json
[
  {
    "travelId": "7687900",
    "source": "草原观赏季，专程从北京赶到内蒙古..."
  }
]
```

### Output Format

JSON file with analysis results:

```json
[
  {
    "travelId": "7687900",
    "speed": 1.408708,
    "volume": 0.945340,
    "circuitousness": 0.043203
  }
]
```

## Command-Line Arguments

- `--data_file` (required): Path to input JSON file
- `--output_file`: Output file name (default: `travelogue_analysis_results.json`)
- `--clip_model`: Hugging Face model name (default: `openai/clip-vit-base-patch32`)
- `--target_chunks`: Chunks per document (default: 100)
- `--limit`: Maximum documents to process (optional)

## Example Usage

### Basic Analysis
```bash
python chinese_travelogue_analysis.py --data_file ../saved/data/data.json
```

### Limited Processing
```bash
python chinese_travelogue_analysis.py \
    --data_file ../saved/data/data.json \
    --limit 100 \
    --output_file sample_results.json
```

### Different CLIP Model
```bash
python chinese_travelogue_analysis.py \
    --data_file ../saved/data/data.json \
    --clip_model openai/clip-vit-large-patch14
```

## Features Explained

### Speed
- Average Euclidean distance between consecutive chunk embeddings
- Measures how quickly the narrative moves through semantic space
- Higher values indicate more dynamic narratives

### Volume
- Volume of the convex hull containing all chunk embeddings
- Measures the semantic diversity of the narrative
- Higher values indicate more varied content

### Circuitousness
- Ratio of total path length to direct path length
- Measures how much the narrative "wanders"
- Values > 1 indicate non-linear narratives

## Performance Notes

- Processing ~3600 travelogues takes approximately 30-60 minutes on GPU
- Memory usage scales with batch size and model size
- CPU processing is significantly slower but works

## Troubleshooting

### Common Issues

1. **Import Error: transformers**
   ```bash
   pip install transformers torch
   ```

2. **CUDA Out of Memory**
   - Reduce batch size or use CPU
   - Process in smaller chunks with `--limit`

3. **Chinese Text Encoding**
   - Ensure JSON file is UTF-8 encoded
   - Check for malformed JSON

4. **Empty Results**
   - Verify data file path
   - Check JSON format matches expected structure

### Performance Optimization

1. **Use GPU if available**
   - CUDA-enabled PyTorch installation
   - Automatic GPU detection

2. **Batch Processing**
   - Process large datasets in chunks
   - Use `--limit` for testing

3. **Model Selection**
   - `openai/clip-vit-base-patch32`: Fastest, 512D
   - `openai/clip-vit-large-patch14`: Slower, 768D, higher quality

## Testing

Run the test suite to verify everything works:

```bash
python test_chinese_analysis.py
```

Expected output:
```
✅ PASS Chinese Text Processor
✅ PASS CLIP Embedding  
✅ PASS Feature Extraction
✅ PASS Data Loading
✅ PASS Full Pipeline

Results: 5/5 tests passed
🎉 All tests passed! The implementation is ready.
```

## Integration with Main Project

This implementation is completely self-contained in the `model/` directory and does not modify the existing project files. It can be used independently or integrated as needed.

To use from the main project directory:

```bash
cd model
python chinese_travelogue_analysis.py --data_file ../saved/data/data.json
```

## Next Steps

1. Run tests to verify setup
2. Process sample data to validate results
3. Analyze full dataset for research insights
4. Compare results with other embedding methods if needed
