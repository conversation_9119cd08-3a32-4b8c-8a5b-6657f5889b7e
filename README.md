# Word Embedding Measures

Python implementation of "How quantifying the shape of stories predicts their success" by <PERSON><PERSON><PERSON> et al. This repository is intended for academic abstracts but can easily be generalized to other domains, including those used in the paper (e.g. movies). **This is not my paper.** You can find it [here](https://www.pnas.org/content/118/26/e2011695118.short?rss=1).

## Installation

You should create a fresh environment and install all dependencies either using `pip install -r requirements.txt` or through the `environment.yml` for conda. Note that the `environment.yml` may contain extra packages which may not be necessary for this project.

如果打算处理的数据包含词汇表以外的单词，需要对模型进行微调，因此需要下载 `.bin` 版本的模型，并设置参数 `--train_model`

如果想使用一个开箱即用的模型，下载 `.vec` 文件

You will also need to download some form of a word embedding model. In this repositoy, we use [FastText](https://fasttext.cc/) whose models can be downloaded [here](https://fasttext.cc/docs/en/english-vectors.html). Note that if you intend to use data with out of vocabulary words, you need to fine-tune the model and thus you will need to download the `.bin` version. If you want to use an out of box model, you can simply use the `.vec` version. You can specify the location of your model using the `--proj_dir` and `--model_name` arguments. To indicate model training, use the `--train_model` flag.

You will also need to specify the location of your data using the `--data_file` argument. If this argument is a directory, you will need to specify the type of file you want to read in using the `--data_file_type` argument. Note that all of this data should be stored in a directory which is specified by the `--proj_dir` argument. 

### Example Data/Model Structure

As an example, I store my data and models in a directory like this:

```
    .
    ├── .gitignore                   
    ├── utils/                   # Utils folder
    ├── saved/
    │   ├── data/
    │   │   └── dataset.json     # dataset
    │   ├── model/
    │   │   └── fasttext.vec     # fasttext model
    ├── requirements.txt         # requirements
    ├── environment.yml          # conda environment
    ├── main.py                  # main file to run
    └── README.md   
```

In this case, `--proj_dir saved/`, `--model_name model/fasttext.vec`, and `--data_file data/dataset.json`.

### Academic Abstracts -- dataset

Since this repository is meant to analyze the values of speed, volume, and circuitousness for academic abstracts, our dataset had to contain academic abstracts and the number of citations each paper received. I obtained abstracts from [this Kaggle dataset](https://www.kaggle.com/kmader/aminer-academic-citation-dataset?select=dblp-ref-0.json). You can get this dataset by doing the following:

```json
{"abstract": "The purpose of this study is to develop a learning tool for high school students studying the scientific aspects of information and communication net- works. More specifically, we focus on the basic principles of network proto- cols as the aim to develop our learning tool. Our tool gives students hands-on experience to help understand the basic principles of network protocols.", "authors": ["Makoto Satoh", "Ryo Muramatsu", "Mizue Kayama", "Kazunori Itoh", "Masami Hashimoto", "Makoto Otani", "Michio Shimizu", "Masahiko Sugimoto"], "n_citation": 0, "references": ["51c7e02e-f5ed-431a-8cf5-f761f266d4be", "69b625b9-ebc5-4b60-b385-8a07945f5de9"], "title": "Preliminary Design of a Network Protocol Learning Tool Based on the Comprehension of High School Students: Design by an Empirical Study Using a Simple Mind Map", "venue": "international conference on human-computer interaction", "year": 2013, "id": "00127ee2-cb05-48ce-bc49-9de556b93346"}
```

```bash
wget https://www.kaggle.com/kmader/aminer-academic-citation-dataset/download/o0mFH8IcsQHZEJ2HX1E1%2Fversions%2FzOZutSMcvhpIpY7AXXtt%2Ffiles%2Fdblp-ref-0.json?datasetVersionNumber=2
```

```python
import kagglehub
# Download latest version
path = kagglehub.dataset_download("kmader/aminer-academic-citation-dataset")
print("Path to dataset files:", path)
```

```python
import nltk
nltk.download('stopwords') 
nltk.download('wordnet')
```

## Running the code

```python
python main.py --model_name model/fasttext.vec --data_file data/dataset.json --proj_dir saved/ 
"""
--proj_dir 模型路径
--model_name 模型名称
--train_model 是否训练模型
--data_file 数据文件
--data_file_type 数据文件类型
""" 
```

下面这部分有点不理解了？

When running for the first time, you will have to generate the chunk embeddings of your entire dataset and potentially train the FastText model. You should use the `--train_model` flag and leave the `--chunk_embs_file` argument as empty. The `--chunk_embs_file` argument is meant to load in a set of existing embeddings to avoid the cost of training the model/creating the embeddings each run.

> 第一次运行代码，将根据整个数据集生成一个 chunk embeddings，后续运行代码，加上 `--chunk_embs_file` 就可直接加载chunk的embedding。

The two other arguments to consider are the `--limit` and `--T` arguments. The former sets a cap on the amount of data used and the latter represents the number of chunks a document is broken into.

> 理解：`--limit` 表示总数据的块数上限；`-T` 表示一个文档被分割成的文本块数量


首次使用 .bin 或 .vec 都会生成一份 chunk_embs.txt 文件，这份文件里记录了chunk的embedding

.bin 包含完整的模型，可以微调或训练
.vec 只有预训练词向量，无法继续训练

加载 .bin 模型后，会进入训练（扩展词汇的向量）过程，训练好后，对分块后的文本生成 embedding，并保存到 chunk_embs.txt 文件中

首次运行使用 --train_model 参数，用于微调 .bin 模型或预训练 .vec 中不存在词及词向量


### Running for the first time
You should ideally use the following command (assuming the project directory, data, and model are the same as the default arguments):

`python main.py --train_model`

`python main.py --model_name model/cc.en.300.bin --data_file data/dataset.json --proj_dir saved/ --train_model`

Note that you can change the values for `--limit` and `--T` based on your needs.

### Running on existing chunks
You should use the following command (assuming the project directory, data, and model are the same as the default arguments):

> 理解：非第一次运行，就可以加上 `--chunk_embs_file`，加载已有的embedding，避免训练开销或重复生成 chunk embedding 文件

`python main.py --chunk_embs_file data/chunk_embs.txt`  

Note that you can change the values for `--limit` and `--T` based on your needs.


---


### Running on 16k Persuasive Pairs
As **an example of running this repository with custom data**, you can run the following command: 

`python main.py --limit 10000 --data_file_type xml --data_file ../persuasive_classifier/16k_persuasiveness/data/UKPConvArg1Strict-XML/ --model_name fasttext_model/cc.en.300.bin --train_model`

And if the chunk embeddings were already calculated, you could run the following:

`python main.py --limit 10000 --data_file_type xml --data_file ../persuasive_classifier/16k_persuasiveness/data/UKPConvArg1Strict-XML/ --chunk_embs_file data/16k_chunk_embs.txt`


## Citation

If you use this work in your experiments, please cite the original author's paper with the following BibTex:

```
@article{
doi:10.1073/pnas.2011695118,
author = {Olivier Toubia  and Jonah Berger  and Jehoshua Eliashberg },
title = {How quantifying the shape of stories predicts their success},
journal = {Proceedings of the National Academy of Sciences},
volume = {118},
number = {26},
pages = {e2011695118},
year = {2021},
doi = {10.1073/pnas.2011695118},
URL = {https://www.pnas.org/doi/abs/10.1073/pnas.2011695118},
eprint = {https://www.pnas.org/doi/pdf/10.1073/pnas.2011695118},
abstract = {Why are some narratives (e.g., movies) or other texts (e.g., academic papers) more successful than others? Narratives are often described as moving quickly, covering lots of ground, or going in circles, but little work has quantified such movements or tested whether they might explain success. We use natural language processing and machine learning to analyze the content of almost 50,000 texts, constructing a simple set of measures (i.e., speed, volume, and circuitousness) that quantify the semantic progression of discourse. While movies and TV shows that move faster are liked more, TV shows that cover more ground are liked less. Academic papers that move faster are cited less, and papers that cover more ground or are more circuitous are cited more. Narratives, and other forms of discourse, are powerful vehicles for informing, entertaining, and making sense of the world. But while everyday language often describes discourse as moving quickly or slowly, covering a lot of ground, or going in circles, little work has actually quantified such movements or examined whether they are beneficial. To fill this gap, we use several state-of-the-art natural language-processing and machine-learning techniques to represent texts as sequences of points in a latent, high-dimensional semantic space. We construct a simple set of measures to quantify features of this semantic path, apply them to thousands of texts from a variety of domains (i.e., movies, TV shows, and academic papers), and examine whether and how they are linked to success (e.g., the number of citations a paper receives). Our results highlight some important cross-domain differences and provide a general framework that can be applied to study many types of discourse. The findings shed light on why things become popular and how natural language processing can provide insight into cultural success.}}
```

You can also link this GitHub repository for those who may want to build upon this work.


## about the chunk_embs.txt file

首行注释：# 30000,20,300
30000 = 处理的文档总数
20 = 每篇文档被切成的块数（即参数 T）
300 = 每个向量的维度（embedding 维度）


数据格式：其余每行是用逗号分隔的浮点数，代表一个“文本块”的 embedding。
共 600,000 行（30,000 篇文档 × 20 块/文档）
每行 300 个数值（对应 300 维向量）
数值是该块内所有词向量的平均值

文件示例：
6.639999896287918091e-02,-2.549999952316284180e-02,-8.133333176374435425e-03,...
本质上，这个文件把一个三维数组 [文档数, 每文档块数, 向量维度] 拉平成二维后保存。



## `word_embedding_notebook.ipynb` 的作用是什么？

经过分析，该笔记本是一个**交互式开发与测试环境**，主要用途如下：

### 1. 开发与实验
- **交互式测试**主流程的各个组件  
- 通过 `Args` 类**灵活设置参数**  
- 提供 **train / load / test** 三种运行模式

### 2. 三种运行模式

**Train 模式**（144–159 行）：  
- 从零开始训练 FastText 模型  
- 同时生成 chunk embedding  
- 使用 `.bin` 模型以便继续训练  

**Load 模式**（160–171 行）：  
- 直接加载已保存的 `chunk_embs.txt`  
- 跳过训练，快速迭代  

**Test 模式**（172–221 行）：  
- **用简单句子做快速测试**  
- 验证叙事特征（speed、volume、circuitousness）  
  ```python
  sentences = [
      'The food here is ok but not worth the price.',
      'The food is mediocre and not worth the ridiculous price.',
      'The food is good but not worth the horrible customer service.',
      'The pizza and burgers are good but not worth the wait times.'
  ]
  ```

### 3. 支持 MATLAB 格式
- 提供 `from_MATLAB` 标志，可导入 MATLAB 格式的 embedding（如 `toubia_embs.txt`）

### 4. 特征分析与可视化
- **交互式计算** speed、volume、circuitousness 等叙事特征  
- **绘图功能**，直观展示叙事轨迹  
- **逐特征调试**（例如中断单元格中计算 circuitousness）

### 5. 开发优势
- **快速原型**：不必跑完整 `main.py`  
- **逐步调试**：embedding 与特征提取可单步执行  
- **调参实验**：随时改参数看效果  
- **结果可视化**：更易理解特征行为  

总结：这个笔记本为研究者提供了一个**沙盒环境**，可以  
- 测试不同配置  
- 可视化叙事特征  
- 交互式排错  
- 先在少量数据上做实验，再扩展到大规模学术语料。