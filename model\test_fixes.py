#!/usr/bin/env python3
"""
Test script to verify the fixes for empty source text and JSON serialization
"""

import sys
import os
import json
import numpy as np

# Add model directory to path
sys.path.append(os.path.dirname(__file__))

from chinese_travelogue_analysis import ChineseTravelogueAnalyzer

def test_empty_source_handling():
    """Test handling of empty source text."""
    print("🔍 Testing Empty Source Text Handling...")
    print("-" * 50)
    
    # Test data with empty and null source fields
    test_data = [
        {'travelId': 'test001', 'source': '草原观赏季，专程从北京赶到内蒙古乌兰布统大草原。'},
        {'travelId': 'test002', 'source': ''},  # Empty string
        {'travelId': 'test003', 'source': None},  # Null value
        {'travelId': 'test004', 'source': '   '},  # Whitespace only
        {'travelId': 'test005'},  # Missing source field
        {'travelId': 'test006', 'source': '西藏拉萨的布达拉宫是世界文化遗产。'}
    ]
    
    try:
        # Create analyzer (will fail if transformers not installed, but that's OK for this test)
        analyzer = ChineseTravelogueAnalyzer()
        
        results = []
        for i, travelogue in enumerate(test_data):
            print(f"Processing travelogue {i+1}: {travelogue.get('travelId')}")
            try:
                result = analyzer.analyze_single_travelogue(travelogue)
                results.append(result)
                print(f"  Result: {result}")
            except Exception as e:
                print(f"  Error: {e}")
                # Add default result
                results.append({
                    'travelId': str(travelogue.get('travelId', f'error_{i}')),
                    'speed': 0.0,
                    'volume': 0.0,
                    'circuitousness': 0.0
                })
        
        print(f"\n✅ Processed {len(results)} travelogues")
        
        # Test JSON serialization
        print("\n🔍 Testing JSON Serialization...")
        try:
            json_str = json.dumps(results, ensure_ascii=False, indent=2)
            print("✅ JSON serialization successful")
            
            # Verify default values for empty sources
            empty_results = [r for r in results if r['travelId'] in ['test002', 'test003', 'test004', 'test005']]
            print(f"\n📊 Default values for empty sources ({len(empty_results)} items):")
            for result in empty_results:
                print(f"  {result['travelId']}: speed={result['speed']}, volume={result['volume']}, circuitousness={result['circuitousness']}")
            
            return True
            
        except Exception as e:
            print(f"❌ JSON serialization failed: {e}")
            return False
            
    except ImportError:
        print("⚠️  Transformers not installed - testing with simulated data")
        
        # Simulate the analysis with numpy values to test JSON serialization
        results = []
        for i, travelogue in enumerate(test_data):
            travel_id = travelogue.get('travelId', f'test_{i}')
            source_text = travelogue.get('source', '')
            
            if not source_text or not source_text.strip():
                print(f"Warning: Empty source text for travelogue {travel_id}")
                result = {
                    'travelId': str(travel_id),
                    'speed': 0.0,
                    'volume': 0.0,
                    'circuitousness': 0.0
                }
            else:
                # Simulate numpy float32 values (the problematic type)
                result = {
                    'travelId': str(travel_id),
                    'speed': np.float32(1.234567),  # This would cause JSON error
                    'volume': np.float32(0.987654),
                    'circuitousness': np.float32(0.123456)
                }
            
            results.append(result)
        
        print(f"✅ Simulated {len(results)} results")
        
        # Test the JSON serialization fix
        print("\n🔍 Testing JSON Serialization Fix...")
        try:
            # This should fail with the original code
            json_str = json.dumps(results, ensure_ascii=False, indent=2)
            print("❌ This should have failed - numpy types not handled")
            return False
        except TypeError as e:
            print(f"✅ Expected error caught: {e}")
            
            # Now test the fix
            analyzer = ChineseTravelogueAnalyzer()
            fixed_results = analyzer._ensure_json_serializable(results)
            
            json_str = json.dumps(fixed_results, ensure_ascii=False, indent=2)
            print("✅ JSON serialization fix successful")
            
            # Verify types are converted
            for result in fixed_results:
                for key, value in result.items():
                    if key != 'travelId':
                        assert isinstance(value, float), f"Value {key}={value} is not Python float"
            
            print("✅ All numeric values converted to Python float")
            return True

def test_json_serialization_edge_cases():
    """Test various edge cases for JSON serialization."""
    print("\n🔍 Testing JSON Serialization Edge Cases...")
    print("-" * 50)
    
    # Create test data with various numpy types
    test_results = [
        {
            'travelId': 'test001',
            'speed': np.float32(1.23),
            'volume': np.float64(4.56),
            'circuitousness': np.int32(7)
        },
        {
            'travelId': 'test002',
            'speed': float('inf'),  # Infinity
            'volume': float('nan'),  # NaN
            'circuitousness': 0.0
        },
        {
            'travelId': 'test003',
            'speed': np.array([1.0, 2.0]),  # Array (shouldn't happen but test anyway)
            'volume': 1.0,
            'circuitousness': 2.0
        }
    ]
    
    analyzer = ChineseTravelogueAnalyzer()
    
    try:
        fixed_results = analyzer._ensure_json_serializable(test_results)
        json_str = json.dumps(fixed_results, ensure_ascii=False, indent=2)
        print("✅ Edge cases handled successfully")
        
        # Print the results
        for result in fixed_results:
            print(f"  {result['travelId']}: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Edge case handling failed: {e}")
        return False

def main():
    """Run all tests."""
    print("Chinese Travelogue Analysis - Fix Verification")
    print("=" * 60)
    
    tests = [
        ("Empty Source Handling", test_empty_source_handling),
        ("JSON Serialization Edge Cases", test_json_serialization_edge_cases),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All fixes verified! The implementation handles:")
        print("   ✅ Empty source text (returns default values: 0.0)")
        print("   ✅ JSON serialization (converts numpy types to Python types)")
        print("   ✅ Error handling (graceful degradation)")
        print("   ✅ Edge cases (inf, nan, arrays)")
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please check the fixes.")

if __name__ == "__main__":
    main()
