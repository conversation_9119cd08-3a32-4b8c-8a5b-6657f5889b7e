# Chinese Travelogue Narrative Analysis - Implementation Plan
*Date: August 9, 2025*

## 🎯 Project Overview

This document outlines the detailed implementation plan for adapting the word embedding measures project to analyze Chinese travelogues using CLIP embeddings instead of FastText. The goal is to process 6000 Chinese travelogues (~4000 characters each) and extract narrative features (speed, volume, circuitousness) while maintaining full backward compatibility.

## 📋 Requirements Summary

- **Dataset**: 6000 Chinese travelogues (4000 characters each)
- **Chunking**: Sentence-based segmentation targeting ~100 chunks per travelogue
- **Embeddings**: CLIP multilingual model (512-dimensional)
- **Features**: Preserve existing narrative analysis (speed, volume, circuitousness)
- **Compatibility**: Maintain existing FastText functionality

## 🔍 Key Questions & Answers

### Q1: Travelogue Dataset Content Requirements
**Answer**: Only text content is required. The system maps it to the existing `ABSTRACT` field.

**Recommended Structure**:
```json
{
  "text": "今天我们来到了北京故宫。这里的建筑真是令人叹为观止...",
  "location": "北京", 
  "date": "2023-05-15",
  "author": "张三"
}
```

### Q2: Chinese Punctuation for Segmentation
**Enhanced Punctuation Set**:
- **Primary**: `。！？；` + paragraph breaks (`\n\n`)
- **Secondary**: `：…` for fine-tuning chunk balance
- **Regex**: `r'[。！？；]|\n\s*\n'` (primary) + `r'[：…]'` (secondary)

### Q3: CLIP Model Selection
**Chosen**: `sentence-transformers/clip-ViT-B-32-multilingual-v1`

**Rationale**:
- ✅ Multilingual optimization (better Chinese support)
- ✅ Text-focused semantic understanding
- ✅ Optimized for narrative analysis tasks
- ✅ Efficient batching in sentence-transformers ecosystem

**vs `openai/clip-vit-base-patch32`**:
- ❌ Primarily English-trained
- ❌ Image-text focused (not needed for our use case)
- ❌ Limited Chinese language capabilities

## 🏗️ Detailed Task Breakdown

### Task 1: Abstract Embedding Interface Design
**Priority**: Critical Foundation
**Estimated Time**: 2-3 days

**Objective**: Create unified interface supporting both FastText and CLIP embeddings with dynamic dimension detection.

**Implementation Details**:
```python
# utils/embeddings.py - New Abstract Interface
class EmbeddingModel(ABC):
    @abstractmethod
    def get_embeddings(self, chunks: List[str]) -> List[np.ndarray]:
        pass
    
    @abstractmethod
    def get_embedding_dim(self) -> int:
        pass

class FastTextEmbedding(EmbeddingModel):
    def __init__(self, model_path: str):
        self.model = load_fasttext(model_path)
    
    def get_embeddings(self, chunks: List[str]) -> List[np.ndarray]:
        # Wrap existing get_chunk_embeddings logic
        pass
    
    def get_embedding_dim(self) -> int:
        return 300

class CLIPEmbedding(EmbeddingModel):
    def __init__(self, model_name: str = "sentence-transformers/clip-ViT-B-32-multilingual-v1"):
        from sentence_transformers import SentenceTransformer
        self.model = SentenceTransformer(model_name)
    
    def get_embeddings(self, chunks: List[str]) -> List[np.ndarray]:
        # Batch process chunks for efficiency
        return self.model.encode(chunks)
    
    def get_embedding_dim(self) -> int:
        return 512
```

**Key Changes**:
1. Modify `get_chunk_embeddings()` to use dynamic dimensions
2. Add factory function `create_embedding_model()`
3. Ensure backward compatibility with existing FastText calls

**Verification Criteria**:
- [ ] FastText embeddings work unchanged (300-dim)
- [ ] CLIP embeddings generate correct 512-dim vectors
- [ ] Dynamic dimension detection works correctly
- [ ] Existing code paths remain functional

### Task 2: Chinese Text Processor Implementation
**Priority**: High
**Estimated Time**: 2-3 days

**Objective**: Implement Chinese sentence segmentation and balanced chunking strategy.

**Implementation Details**:
```python
# utils/data.py - Text Processing Abstraction
class TextProcessor(ABC):
    @abstractmethod
    def get_chunks(self, document: str, target_chunks: int) -> List[str]:
        pass

class ChineseProcessor(TextProcessor):
    def __init__(self):
        # Primary segmentation pattern
        self.primary_pattern = re.compile(r'[。！？；]|\n\s*\n')
        # Secondary for fine-tuning
        self.secondary_pattern = re.compile(r'[：…]')
    
    def get_chunks(self, document: str, target_chunks: int = 100) -> List[str]:
        # 1. Split by primary punctuation
        sentences = self.primary_pattern.split(document)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        # 2. Group sentences to reach target chunk count
        if len(sentences) > target_chunks:
            # Merge sentences
            chunks = self._merge_sentences(sentences, target_chunks)
        elif len(sentences) < target_chunks * 0.8:
            # Further split using secondary punctuation
            chunks = self._split_further(sentences, target_chunks)
        else:
            chunks = sentences
        
        return chunks[:target_chunks]  # Ensure exact count
```

**Chunking Strategy**:
1. **Primary Split**: Use `[。！？；]` and paragraph breaks
2. **Balance Check**: Target 100±10 chunks
3. **Merge Strategy**: Combine short sentences if too many chunks
4. **Split Strategy**: Use secondary punctuation if too few chunks
5. **Length Balance**: Aim for relatively uniform chunk lengths

**Verification Criteria**:
- [ ] Chinese sentences correctly segmented
- [ ] Chunk count within target range (90-110)
- [ ] Reasonable length distribution
- [ ] Handles edge cases (very short/long texts)

### Task 3: CLIP Model Integration & Configuration
**Priority**: High  
**Estimated Time**: 2-3 days

**Objective**: Integrate CLIP model with command-line configuration support.

**Dependencies Installation**:
```bash
pip install sentence-transformers torch
```

**Command-Line Extensions**:
```python
# main.py - Extended Arguments
parser.add_argument("--embedding_type", choices=['fasttext', 'clip'], 
                   default='fasttext', help="Embedding model type")
parser.add_argument("--language", choices=['en', 'zh'], 
                   default='en', help="Text language")
parser.add_argument("--clip_model", type=str, 
                   default='sentence-transformers/clip-ViT-B-32-multilingual-v1',
                   help="CLIP model name")
parser.add_argument("--embedding_dim", type=int, 
                   help="Override embedding dimension")
```

**Model Loading Logic**:
```python
def create_embedding_model(args) -> EmbeddingModel:
    if args.embedding_type == 'clip':
        return CLIPEmbedding(args.clip_model)
    else:
        return FastTextEmbedding(args.proj_dir + args.model_name)
```

**Verification Criteria**:
- [ ] CLIP model loads successfully
- [ ] Chinese text embeddings generated correctly
- [ ] Batch processing efficient for large datasets
- [ ] Command-line parameters work as expected
- [ ] Error handling for model loading failures

### Task 4: Dynamic Dimension Support Updates
**Priority**: Medium-High
**Estimated Time**: 1-2 days

**Objective**: Update feature extraction to handle variable embedding dimensions.

**Key Changes**:
```python
# utils/features.py - Dynamic Dimension Support
def get_volume(chunk_emb: list, tolerance: float = 0.01) -> float:
    """Updated to detect dimension dynamically"""
    P = np.array(chunk_emb)
    emb_dim = P.shape[1]  # Dynamic dimension detection
    
    rank = np.linalg.matrix_rank(P, tolerance)
    if rank < emb_dim or (rank == emb_dim and P.shape[0] <= emb_dim):
        # Existing logic with dynamic emb_dim
        pass
    # ... rest of function
```

**File Format Updates**:
```python
# main.py - Chunk Embeddings File Format
def setup_chunk_embeddings(args, model, documents):
    if exists(args.proj_dir + args.chunk_embs_file):
        # Load with dynamic dimension parsing
        with open(args.proj_dir + args.chunk_embs_file, 'r+') as f:
            shape = tuple(map(int, f.readline()[1:].split(',')))
            # shape[2] now could be 300 or 512
    else:
        # Save with correct dimensions
        chunk_embs = generate_embeddings(model, documents, args.T)
        header = ','.join(map(str, chunk_embs.shape))  # Dynamic header
        np.savetxt(file_path, chunk_embs.reshape(-1, chunk_embs.shape[-1]), 
                  header=header, delimiter=',')
```

**Verification Criteria**:
- [ ] Volume calculation works with 512-dim embeddings
- [ ] Speed and circuitousness unaffected by dimension change
- [ ] File format correctly handles both 300 and 512 dimensions
- [ ] Loading existing 300-dim files still works

### Task 5: Chinese Travelogue Data Loader
**Priority**: Medium
**Estimated Time**: 1-2 days

**Objective**: Implement data loading for Chinese travelogue formats.

**Supported Formats**:
```python
# utils/data.py - Travelogue Loading
def load_chinese_travelogues(args) -> List[Dict]:
    """Load Chinese travelogue data"""
    data_path = args.proj_dir + args.data_file
    
    if data_path.endswith('.json'):
        return load_travelogue_json(data_path, args.limit)
    elif data_path.endswith('.txt') or os.path.isdir(data_path):
        return load_travelogue_text(data_path, args.limit)
    else:
        raise ValueError(f"Unsupported travelogue format: {data_path}")

def load_travelogue_json(file_path: str, limit: int) -> List[Dict]:
    """Load from JSON format"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    result = []
    for item in data[:limit]:
        result.append({
            ABSTRACT: item.get('text', item.get('content', '')),
            N_CITATION: 1,  # Default value
            'location': item.get('location', ''),
            'date': item.get('date', ''),
            'length': len(item.get('text', ''))
        })
    return result

def load_travelogue_text(path: str, limit: int) -> List[Dict]:
    """Load from text files"""
    if os.path.isdir(path):
        files = glob.glob(os.path.join(path, '*.txt'))[:limit]
        result = []
        for file_path in files:
            with open(file_path, 'r', encoding='utf-8') as f:
                text = f.read().strip()
                result.append({
                    ABSTRACT: text,
                    N_CITATION: 1,
                    'filename': os.path.basename(file_path),
                    'length': len(text)
                })
        return result
    else:
        # Single text file with multiple travelogues
        with open(path, 'r', encoding='utf-8') as f:
            texts = f.read().split('\n\n')  # Assume double newline separation
        
        return [{
            ABSTRACT: text.strip(),
            N_CITATION: 1,
            'index': i,
            'length': len(text)
        } for i, text in enumerate(texts[:limit]) if text.strip()]
```

**Control Variables for Chinese Travelogues**:
```python
# utils/controls.py - Chinese-specific Controls
def get_chinese_travelogue_controls(data: List[Dict]) -> np.ndarray:
    """Extract control variables for Chinese travelogues"""
    controls = []
    
    # Text length in characters
    lengths = [d.get('length', len(d[ABSTRACT])) for d in data]
    controls.append(np.array(lengths))
    
    # Sentence count (approximate)
    sentence_counts = [len(re.split(r'[。！？]', d[ABSTRACT])) for d in data]
    controls.append(np.array(sentence_counts))
    
    # Location encoding (if available)
    locations = [d.get('location', 'unknown') for d in data]
    location_to_idx = {loc: i for i, loc in enumerate(set(locations))}
    location_indices = [location_to_idx[loc] for loc in locations]
    controls.append(np.array(location_indices))
    
    return np.stack(controls).transpose()
```

**Verification Criteria**:
- [ ] Successfully loads 6000 travelogues
- [ ] Handles both JSON and text formats
- [ ] Memory usage reasonable for large datasets
- [ ] Control variables extracted correctly
- [ ] Error handling for malformed data

### Task 6: Integration Testing & Validation
**Priority**: Critical
**Estimated Time**: 2-3 days

**Objective**: Comprehensive testing and validation of the complete pipeline.

**Test Script Structure**:
```python
# test_chinese_clip.py - Comprehensive Testing
def test_small_sample():
    """Test with small sample of Chinese travelogues"""
    sample_texts = [
        "今天我们来到了北京故宫。这里的建筑真是令人叹为观止！每一个细节都体现了古代工匠的智慧。",
        "上海的夜景非常美丽。外滩的灯光倒映在黄浦江中，形成了一幅动人的画面。我们在这里拍了很多照片。"
    ]
    
    # Test chunking
    processor = ChineseProcessor()
    chunks = [processor.get_chunks(text, 10) for text in sample_texts]
    assert all(8 <= len(chunk_list) <= 12 for chunk_list in chunks)
    
    # Test embeddings
    model = CLIPEmbedding()
    embeddings = [model.get_embeddings(chunk_list) for chunk_list in chunks]
    assert all(emb.shape[1] == 512 for emb_list in embeddings for emb in emb_list)
    
    # Test features
    features = [get_features(emb_list) for emb_list in embeddings]
    assert all('speed' in f and 'volume' in f and 'circuitousness' in f for f in features)

def test_full_pipeline():
    """Test complete pipeline with sample data"""
    # Create test configuration
    args = create_test_args()
    args.embedding_type = 'clip'
    args.language = 'zh'
    args.T = 100
    
    # Run pipeline
    model = create_embedding_model(args)
    data = load_chinese_travelogues(args)
    chunk_embs = setup_chunk_embeddings(args, model, get_data_property(data, ABSTRACT))
    features = [get_features(chunk_emb) for chunk_emb in chunk_embs]
    
    # Validate results
    assert len(features) == len(data)
    assert all(isinstance(f['speed'], float) for f in features)
    assert all(isinstance(f['volume'], float) for f in features)

def benchmark_performance():
    """Compare FastText vs CLIP performance"""
    # Test same data with both models
    # Record processing time and memory usage
    # Compare feature distributions
```

**Backward Compatibility Tests**:
```python
def test_backward_compatibility():
    """Ensure existing FastText workflow unchanged"""
    # Test original commands still work
    # Test existing chunk_embs.txt files load correctly
    # Test feature extraction produces same results
```

**Documentation Updates**:
```markdown
# README.md - Usage Examples

## Chinese Travelogue Analysis

### First Run (Generate Embeddings)
```bash
python main.py --embedding_type clip --language zh --data_file data/chinese_travelogues.json --T 100 --limit 6000
```

### Subsequent Runs (Load Existing Embeddings)
```bash
python main.py --embedding_type clip --language zh --chunk_embs_file data/chinese_chunk_embs.txt --T 100
```

### Mixed Usage (FastText for English, CLIP for Chinese)
```bash
# English academic papers (original)
python main.py --embedding_type fasttext --data_file data/academic_papers.json

# Chinese travelogues (new)
python main.py --embedding_type clip --language zh --data_file data/travelogues.json
```
```

**Verification Criteria**:
- [ ] End-to-end pipeline executes without errors
- [ ] Generated features are reasonable and interpretable
- [ ] Performance acceptable for 6000 travelogues
- [ ] Backward compatibility 100% maintained
- [ ] Documentation clear and comprehensive
- [ ] Test coverage >80%

## 🚀 Implementation Timeline

| Week | Tasks | Deliverables |
|------|-------|-------------|
| Week 1 | Task 1 + Task 2 | Abstract interfaces + Chinese text processing |
| Week 2 | Task 3 + Task 4 | CLIP integration + Dynamic dimensions |
| Week 3 | Task 5 + Task 6 | Data loading + Testing & validation |

## 📊 Expected Outcomes

1. **Functionality**: Complete Chinese travelogue narrative analysis capability
2. **Performance**: Process 6000 travelogues in reasonable time (<2 hours)
3. **Quality**: Meaningful narrative features for Chinese text
4. **Compatibility**: Zero impact on existing FastText workflows
5. **Extensibility**: Framework for future language/model additions

## 🔧 Technical Specifications

- **Python**: 3.8+
- **Dependencies**: sentence-transformers, torch, numpy, scipy
- **Memory**: ~8GB RAM for full dataset processing
- **Storage**: ~2GB for CLIP model + embeddings
- **Processing Time**: ~1-2 hours for 6000 travelogues (first run)

---

*This plan ensures a systematic, well-tested implementation that maintains the project's academic rigor while extending its capabilities to Chinese narrative analysis.*
